/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAffix: typeof import('ant-design-vue/es')['Affix']
    AAlert: typeof import('ant-design-vue/es')['Alert']
    AAnchor: typeof import('ant-design-vue/es')['Anchor']
    AAutoComplete: typeof import('ant-design-vue/es')['AutoComplete']
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    ABackTop: typeof import('ant-design-vue/es')['BackTop']
    ABadge: typeof import('ant-design-vue/es')['Badge']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACardMeta: typeof import('ant-design-vue/es')['CardMeta']
    ACascader: typeof import('ant-design-vue/es')['Cascader']
    ACol: typeof import('ant-design-vue/es')['Col']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    AddFollowModal: typeof import('./src/components/addFollowModal/index.vue')['default']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AFloatButton: typeof import('ant-design-vue/es')['FloatButton']
    AFloatButtonGroup: typeof import('ant-design-vue/es')['FloatButtonGroup']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AImage: typeof import('ant-design-vue/es')['Image']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APagination: typeof import('ant-design-vue/es')['Pagination']
    APopover: typeof import('ant-design-vue/es')['Popover']
    AQrcode: typeof import('ant-design-vue/es')['QRCode']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    AResult: typeof import('ant-design-vue/es')['Result']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASegmented: typeof import('ant-design-vue/es')['Segmented']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASkeleton: typeof import('ant-design-vue/es')['Skeleton']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATimeline: typeof import('ant-design-vue/es')['Timeline']
    ATimelineItem: typeof import('ant-design-vue/es')['TimelineItem']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATypographyText: typeof import('ant-design-vue/es')['TypographyText']
    ATypographyTitle: typeof import('ant-design-vue/es')['TypographyTitle']
    Boolean: typeof import('./src/components/searchCriteria/inputComps/boolean.vue')['default']
    CompanyAvatar: typeof import('./src/components/companyAvatar/index.vue')['default']
    CompanyCard: typeof import('./src/components/companyCard/index.vue')['default']
    CompanyDirectory: typeof import('./src/components/companyDirectory/index.vue')['default']
    CompanyWrapper: typeof import('./src/components/companyWrapper/index.vue')['default']
    CountdownTimer: typeof import('./src/components/layouts/headerMenu/vipModal/countdownTimer.vue')['default']
    Date: typeof import('./src/components/searchCriteria/inputComps/date.vue')['default']
    DirectoryDetailModal: typeof import('./src/components/companyDirectory/directoryDetailModal.vue')['default']
    EllipsisTag: typeof import('./src/components/ellipsisTag/index.vue')['default']
    Empty: typeof import('./src/components/empty/index.vue')['default']
    Enum: typeof import('./src/components/searchCriteria/inputComps/enum.vue')['default']
    ExecutiveSaidCard: typeof import('./src/components/executiveSaidCard/index.vue')['default']
    FilterForm: typeof import('./src/components/filterForm/index.vue')['default']
    HeaderMenu: typeof import('./src/components/layouts/headerMenu/index.vue')['default']
    HoverTooltipText: typeof import('./src/components/hoverTooltipText/index.vue')['default']
    Id: typeof import('./src/components/searchCriteria/inputComps/id.vue')['default']
    Layouts: typeof import('./src/components/layouts/index.vue')['default']
    LoginLayout: typeof import('./src/components/layouts/LoginLayout.vue')['default']
    LoginModal: typeof import('./src/components/loginModal/index.vue')['default']
    Logo: typeof import('./src/components/layouts/headerMenu/logo.vue')['default']
    Methodology: typeof import('./src/components/methodologyModal/methodology.vue')['default']
    MethodologyModal: typeof import('./src/components/methodologyModal/index.vue')['default']
    More: typeof import('./src/components/actionIcon/more.vue')['default']
    NewsCard: typeof import('./src/components/newsCard/index.vue')['default']
    NewsCardDetailModal: typeof import('./src/components/newsCard/newsCardDetailModal.vue')['default']
    Number: typeof import('./src/components/searchCriteria/inputComps/number.vue')['default']
    Password: typeof import('./src/components/loginModal/password.vue')['default']
    QrCode: typeof import('./src/components/loginModal/qrCode.vue')['default']
    RedeemCoupons: typeof import('./src/components/layouts/headerMenu/vipModal/redeemCoupons.vue')['default']
    RenderFormItem: typeof import('./src/components/filterForm/components/renderFormItem.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RouteView: typeof import('./src/components/layouts/RouteView.vue')['default']
    ScoreDetail: typeof import('./src/components/companyCard/scoreDetail/index.vue')['default']
    SearchBox: typeof import('./src/components/layouts/headerMenu/searchBox.vue')['default']
    SearchCriteria: typeof import('./src/components/searchCriteria/index.vue')['default']
    SearchFieldBox: typeof import('./src/components/searchCriteria/searchFieldBox.vue')['default']
    String: typeof import('./src/components/searchCriteria/inputComps/string.vue')['default']
    TermLayout: typeof import('./src/components/layouts/TermLayout.vue')['default']
    UserMenu: typeof import('./src/components/layouts/headerMenu/userMenu/index.vue')['default']
    UserPassword: typeof import('./src/components/layouts/headerMenu/userMenu/userPassword.vue')['default']
    VipModal: typeof import('./src/components/layouts/headerMenu/vipModal/index.vue')['default']
  }
}
