<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-07-29 15:36:09
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-12 16:03:23
 * @FilePath: /corp-elf-web-consumer/src/components/searchBox/searchFieldBox.vue
 * @Description: 
-->
<template>
  <a-row :gutter="16" class="searchFieldBox" :id="id">
    <a-col :span="4" class="menuList">
      <div v-for="(value, key, index) in field" :key="index" @click="handlerFirstFieldClick(key, value)" class="firstFieldItem">
        <p :class="['firstFieldText', key === activityKey ? 'mainColorBg' : '']">
          {{ key }}
        </p>
      </div>
    </a-col>

    <a-col :span="20" class="activityFieldList" @scroll="handlerScroll">
      <div v-for="(secondFieldList, firstField, index) in field" :key="index" class="firstField" :id="firstField">
        <div v-for="(thirdFieldList, secondField, index) in secondFieldList" :key="index" class="secondField">
          <p class="secondFieldText">{{ secondField }}</p>

          <div class="thirdField">
            <div
              v-for="(thirdFieldItem, index) in thirdFieldList"
              :key="index"
              :class="['thirdFieldText', disabledNode.includes(thirdFieldItem.fieldIndex) ? 'disabled' : '']"
              @click="handlerClickThirdFieldItem(thirdFieldItem)"
            >
              <!-- <span v-html="thirdFieldItem.fieldName.replace(/\(/g, '<br/>(')"></span> -->
              {{ thirdFieldItem.fieldName }}
            </div>
          </div>
        </div>
      </div>
    </a-col>
  </a-row>
</template>

<script lang="ts">
import { companySearchField } from '@/api/api'
import { randomUUID } from '@/utils/util'
import { debounce, isEmpty, minBy } from 'lodash-es'
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'SearchFieldBox',
  props: {
    path: {
      type: String,
      default: '商瞳优选'
    },
    field: {
      type: Object,
      default: () => {},
      required: true
    },
    disabledNode: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    field(newVal) {
      if (this.activityKey === '') {
        const firstKey = Object.keys(this.field)[0] || ''
        const firstValue = this.field[firstKey]
        this.activityKey = firstKey
        // this.activityFieldList = firstValue
      }
    }
  },
  data() {
    return {
      loading: false,
      activityKey: '',
      id: randomUUID()
      // field: [],
      // activityFieldList: []
    }
  },
  created() {
    this.activityKey = this.path
    const firstValue = this.field[this.activityKey]
    // this.activityFieldList = firstValue
  },
  methods: {
    // 处理一级字段点击
    handlerFirstFieldClick(key, value) {
      this.activityKey = key
      // this.activityFieldList = value

      const top = $(`#${this.id} #${key}`).position().top
      const parentScrollTop = $(`#${this.id} .activityFieldList`).scrollTop() as number
      $(`#${this.id} .activityFieldList`).animate({ scrollTop: parentScrollTop + top }, 300)
    },
    // 处理三级字段点击
    handlerClickThirdFieldItem(e) {
      if (!this.disabledNode.includes(e.fieldIndex)) {
        this.$emit('nodeClick', e)
      }
    },
    handlerScroll: debounce(function (e) {
      this.$nextTick(() => {
        const secondFieldList = $(`#${this.id} .secondFieldText`)
          .map(function (index, element) {
            const el = $(element)
            return {
              firstFieldKey: el.parent().parent().attr('id'),
              secondField: el.html(),
              top: Math.abs(el.position().top)
            }
          })
          .filter((index, item) => item.top < 100)
          .toArray()
        const minOffsetTop = minBy(secondFieldList, 'top')
        if (!isEmpty(minOffsetTop)) {
          this.activityKey = minOffsetTop.firstFieldKey
        }
      })
    }, 50)
  }
})
</script>

<style lang="less" scoped>
.searchFieldBox {
  position: relative;
  height: 800px;
  width: 1172px;
  padding: 8px;
  background-color: rgba(204, 204, 204, 0.25);
  border-radius: 8px;
  display: flex;
  align-items: flex-start;

  .ant-row {
    height: 100%;
    width: 100%;
  }

  .menuList {
    overflow: auto;
    // width: 20%;
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
    padding: 8px;
    .firstFieldText {
      border-radius: 8px;
      // width: 146px;
      height: 62px;
      line-height: 62px;
      padding: 0 8px;
      &:hover {
        background-color: fade(#6553ee, 10%);
      }
    }
    // .activityFirstFieldText {
    //   background-color: fade(var(--g-primary-color, #6553ee), 10%);
    // }
  }

  .activityFieldList {
    overflow: auto;
    height: 100%;
    margin-left: 8px;
    flex: 1;
    background-color: #fff;
    border-radius: 8px;
    position: relative;
    .secondField {
      padding: 8px;

      .secondFieldText {
        position: relative;
        padding-left: 16px;
        border-radius: 8px;
        height: 48px;
        line-height: 48px;
        margin-bottom: 12px;

        &::before {
          width: 2px;
          height: 48px;
          background-color: var(--g-primary-color, #6553ee);
          content: '';
          position: absolute;
          top: 0;
          left: 0;
        }
      }
    }

    .thirdField {
      display: flex;
      align-items: flex-start;
      flex-wrap: wrap;

      .disabled {
        cursor: not-allowed !important;
        background-color: #fff !important;
        border-color: #ebeef5 !important;
        color: #c0c4cc !important;
        &:hover {
          box-shadow: 0px 0px 0px #ccc !important;
        }
      }

      .thirdFieldText {
        width: 164px;
        height: 56px;
        padding: 8px;
        margin: 0 12px 12px 0;
        border: 1px solid #f3f5f7;
        border-radius: 8px;

        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;

        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-flex;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;

        &:hover {
          cursor: pointer;
          border-color: fade(#6553ee, 60%);
          box-shadow: 0px 0px 5px #ccc;
        }
      }
    }
  }
}
</style>
