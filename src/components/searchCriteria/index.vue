<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-07-29 15:29:10
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 11:53:02
 * @FilePath: /corp-elf-web-consumer/src/components/searchCriteria/index.vue
 * @Description: 
-->
<template>
  <div class="searchBox">
    <a-spin :spinning="loading">
      <a-form autocomplete="off" ref="searchFormRef" class="searchFormList" :model="conditions">
        <template v-if="isReady">
          <div v-for="(item, index) in conditions" :key="item.uuid" class="searchFormItem">
            <template v-if="item.searchIndex !== 'open_status'">
              <a-form-item>
                <div class="searchFieldBoxContainer">
                  <a-select style="width: 220px" v-if="!isEmpty(item.affixType)" v-model:value="item.affixValue" placeholder="请选择筛选项">
                    <a-select-option v-for="(optionItem, index) in fieldAffix[item.affixType].value" :key="index" :value="optionItem.value"
                      >{{ item.fieldName }}（{{ optionItem.label }}）</a-select-option
                    >
                  </a-select>
                  <a-input v-else readonly placeholder="请选择筛选项" style="width: 220px" v-model:value="item.fieldName"> </a-input>

                  <!-- <a-input
                  readonly
                  placeholder="请选择筛选项"
                  style="width: 180px"
                  v-model:value="item.fieldName"
                  @click="item.isShowSearchFieldBox = !item.isShowSearchFieldBox"
                >
                </a-input>

                <searchFieldBox
                  v-show="item.isShowSearchFieldBox"
                  class="searchFieldBox"
                  :field="fieldList"
                  :path="item.path[0]"
                  :disabledNode="disabledNode"
                  @nodeClick="e => handlerNodeClick(e, 'edit', index)"
                /> -->
                </div>
              </a-form-item>

              <a-form-item>
                <!-- v-if="!item.fieldType.includes('BOOL')" -->
                <a-select
                  placeholder="请选择筛条件"
                  style="width: 120px"
                  v-model:value="item.conditionType"
                  :fieldNames="{ label: 'conditionName', value: 'conditionType' }"
                  :options="searchConditionList[item.fieldType].condition"
                  class="condition"
                  @change="(e, options) => handlerConditionChange(e, options, index)"
                >
                </a-select>
              </a-form-item>

              <a-form-item class="inputItem" :name="[index]" :rules="[{ validator: searchFormItemValueValidator }]">
                <!-- 布尔类型 -->
                <template v-if="item.fieldType.includes('BOOL')">
                  <!-- 布尔类型由于只有一个是否选项，用筛选范围就行了，不用单独再写一个组件 -->
                  <!-- <booleanInput
                  ref="booleanInputRef"
                  v-model:value="conditions[index]"
                  :conditionList="searchConditionList[item.fieldType].condition"
                /> -->
                </template>

                <!-- 枚举类型 -->
                <template v-else-if="item.fieldType.includes('ENUM')">
                  <enumInput
                    ref="enumInputRef"
                    v-model:value="conditions[index]"
                    :conditionList="searchConditionList[item.fieldType].condition"
                    :enumsList="isEmpty(searchEnumsList[item.fieldIndex]) ? [] : searchEnumsList[item.fieldIndex].value"
                    @change="e => handlerEnumChange(e, index)"
                  />
                </template>

                <!-- 日期类型 -->
                <template v-else-if="item.fieldType.includes('DATE')">
                  <dateInput
                    ref="dateInputRef"
                    v-model:value="conditions[index]"
                    :conditionList="searchConditionList[item.fieldType].condition"
                  />
                </template>

                <!-- 数字类型 -->
                <template v-else-if="item.fieldType.includes('NUM')">
                  <numberInput
                    ref="numberInputRef"
                    v-model:value="conditions[index]"
                    :conditionList="searchConditionList[item.fieldType].condition"
                  />
                </template>

                <!-- 字符串类型 -->
                <template v-else-if="item.fieldType.includes('STRING')">
                  <stringInput
                    ref="stringInputRef"
                    v-model:value="conditions[index]"
                    :conditionList="searchConditionList[item.fieldType].condition"
                  />
                </template>

                <!-- ID类型 -->
                <template v-else-if="item.fieldType.includes('ID')">
                  <a-input v-model:value="conditions[index].values[0]" style="width: 100%" placeholder="请输入" />
                </template>
              </a-form-item>

              <a-form-item
                v-if="props.isHaveScore"
                label="得分"
                class="score"
                :name="[index]"
                :rules="[{ validator: searchFormItemScoreValidator }]"
              >
                <!-- :name="['searchForm', index, 'score']"
            :rules="[{ required: true, message: '请输入分数' }]" -->
                <a-input-number
                  v-model:value="conditions[index].score"
                  style="width: 100px"
                  :min="1"
                  :max="100"
                  :precision="0"
                  placeholder="请输入"
                />
              </a-form-item>

              <!-- 删除按钮 -->
              <iconfontIcon icon="icon-delete" @click="deleteIcon(index)" class="deleteIcon hoverPrimaryColor" />
            </template>
          </div>

          <div class="searchFieldBoxContainer">
            <!-- v-on-click-outside="e => handlerClickOutside('add')" -->
            <!-- <a-popover >
              <template #content>
                <searchFieldBox
                  v-show="isShowAddSearchFieldBox"
                  :field="fieldList"
                  :disabledNode="disabledNode"
                  class="searchFieldBox"
                  @nodeClick="e => handlerNodeClick(e, 'add')"
                />
              </template>
            </a-popover> -->

            <a-modal v-model:open="isShowAddSearchFieldBox" :width="1200" :closable="false" :footer="null">
              <searchFieldBox
                :field="fieldList"
                :disabledNode="disabledNode"
                class="searchFieldBox"
                @nodeClick="e => handlerNodeClick(e, 'add')"
              />
            </a-modal>

            <a-form-item name="addConditionBtn" :rules="[{ validator: haveConditionValidator }]">
              <a-button class="flexCenter" @click="isShowAddSearchFieldBox = !isShowAddSearchFieldBox">
                <template #icon>
                  <iconfontIcon icon="icon-add" />
                </template>
                {{ props.conditionBtnText }}
              </a-button>
            </a-form-item>

            <!-- <a-form-item name="addConditionBtn" :rules="[{ validator: haveConditionValidator }]">
              <a-button @click="isShowAddSearchFieldBox = !isShowAddSearchFieldBox" class="flexCenter">
                <template #icon>
                  <iconfontIcon icon="icon-add" />
                </template>
                筛选条件
              </a-button>
            </a-form-item>

            <searchFieldBox
              v-show="isShowAddSearchFieldBox"
              :field="fieldList"
              :disabledNode="disabledNode"
              class="searchFieldBox"
              @nodeClick="e => handlerNodeClick(e, 'add')"
            /> -->
          </div>
        </template>
      </a-form>
    </a-spin>
  </div>
</template>

<script setup lang="ts" name="searchBox">
import { companySearchCondition, companySearchEnums, companySearchField } from '@/api/api'
import searchFieldBox from './searchFieldBox.vue'
import booleanInput from './inputComps/boolean.vue'
import enumInput from './inputComps/enum.vue'
import dateInput from './inputComps/date.vue'
import stringInput from './inputComps/string.vue'
import numberInput from './inputComps/number.vue'
import idInput from './inputComps/id.vue'
import { randomUUID } from '@/utils/util'
import { searchFormItemType } from '~/types/common/searchForm'
import { computed, nextTick, onMounted, ref, toRaw, toRefs } from 'vue'
import { cloneDeep, isArray, isEmpty, isNumber, some, toNumber, uniq } from 'lodash-es'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useSearchField } from '@/store'
import { FormInstance, message } from 'ant-design-vue'

const props = withDefaults(
  defineProps<{
    conditions: searchFormItemType[]
    isHaveScore?: boolean
    defaultScore?: number
    isSetDefaultSearchForm?: boolean
    onlyCondition?: boolean
    conditionBtnText?: string
    /** 省市区限制 */
    areaLimit?: boolean
  }>(),
  {
    conditions: () => [],
    isHaveScore: false,
    isSetDefaultSearchForm: true,
    onlyCondition: true,
    defaultScore: 1,
    conditionBtnText: '筛选条件',
    areaLimit: false
  }
)
// const conditions = ref<searchFormItemType[]>([])
const { conditions } = toRefs(props)
const emit = defineEmits(['update:conditions', 'mountedDone'])

const searchFormRef = ref<FormInstance>()
const isReady = ref(false)
const loading = ref(false)
const isShowAddSearchFieldBox = ref(false)
const { searchConditionList, searchEnumsList, fieldList, fieldAffix } = useSearchField()
// const searchConditionList = ref({})
// const searchEnumsList = ref({})
// const fieldList = ref({})
// const fieldAffix = ref({})
const disabledNode = computed(() => (props.onlyCondition ? conditions.value.map(item => item.fieldIndex) : []))

// // 获取条件
// async function getCompanySearchCondition() {
//   try {
//     const { result } = await companySearchCondition({})
//     searchConditionList.value = result
//   } catch (error) {
//     console.error(error)
//   }
// }
// // 获取枚举类型
// async function getCompanySearchEnums() {
//   try {
//     const { result } = await companySearchEnums({})
//     searchEnumsList.value = result
//   } catch (error) {
//     console.error(error)
//   }
// }
// // 获取所有字段列表
// async function getFieldList() {
//   try {
//     const { result } = await companySearchField({})
//     fieldList.value = result
//   } catch (error) {
//     console.error(error)
//   }
// }

// 校验有没有筛选条件方法
function haveConditionValidator(_: any) {
  const cloneData = toRaw(conditions.value)
  console.log(1221)

  if (cloneData.length === 0) {
    return Promise.reject(new Error('请添加条件'))
  } else {
    return Promise.resolve()
  }
}
// 校验筛选值方法
function searchFormItemValueValidator(_: any) {
  const index = _.fullField
  const item = toRaw(conditions.value[index])
  if (isEmpty(item)) {
    return Promise.resolve()
  }
  if (isEmpty(item.conditionType)) {
    return Promise.reject(new Error('请选择'))
  } else if (props.areaLimit && item.searchIndex === 'ent_area' && item.values.length > 50) {
    return Promise.reject(new Error(`${item.values.length}/50`))
  } else if (item.fieldType.includes('ENUM') && isEmpty(item.values)) {
    return Promise.reject(new Error('请选择'))
  } else if (item.fieldType.includes('STRING') && isEmpty(item.values)) {
    return Promise.reject(new Error('请输入'))
  } else if (item.fieldType.includes('ID') && isEmpty(item.values[0])) {
    return Promise.reject(new Error('请输入'))
  } else if (item.fieldType.includes('DATE')) {
    // && isEmpty(item.dates)
    // return Promise.reject(new Error('请选择查询时间'))
    const isBetween = item.conditionType === 'NBET' || item.conditionType === 'BET'
    if (isBetween) {
      if (isEmpty(item.dates[0]) || isEmpty(item.dates[1])) {
        return Promise.reject(new Error('请选择'))
        // } else if (item.dates[0] > item.dates[1]) {
        //   return Promise.reject(new Error('请输入正确数字'))
      }
    } else {
      if (isEmpty(item.dates[0])) {
        return Promise.reject(new Error('请选择'))
      }
    }
  } else if (item.fieldType.includes('NUM') && !item.fieldType.includes('ENUM')) {
    const isBetween = item.conditionType === 'NBET' || item.conditionType === 'BET'
    if (isBetween) {
      if (!item.nums[0] || !item.nums[1]) {
        return Promise.reject(new Error('请输入'))
      } else if (item.nums[0] > item.nums[1]) {
        return Promise.reject(new Error('请输入'))
      }
    } else {
      if (!item.nums[0]) {
        return Promise.reject(new Error('请输入'))
      }
    }
  }

  return Promise.resolve()
}
// 校验分数方法
function searchFormItemScoreValidator(_: any) {
  const index = _.fullField
  const item = toRaw(conditions.value[index])
  if (isEmpty(item)) {
    return Promise.resolve()
  }

  if (!isNumber(item.score) && isEmpty(item.score)) {
    return Promise.reject(new Error('请输入得分'))
  } else if (isNumber(item.score) && (item.score < 1 || item.score > 100)) {
    return Promise.reject(new Error('请输入正确得分'))
  }

  return Promise.resolve()
}

// 添加条件回掉
function handlerNodeClick(e, type = 'add', index?: number) {
  console.log('e: ', e)
  let temp: searchFormItemType = {
    uuid: e.uuid ? e.uuid : randomUUID(),
    isShowSearchFieldBox: false,
    fieldIndex: e.fieldIndex,
    searchIndex: e.searchIndex,
    fieldName: e.fieldName,
    fieldType: e.fieldType,
    unitName: e.unit_name,
    unitValue: e.unit_value,
    path: [e.path_one, e.path_two, e.fieldName],
    conditionType: undefined,
    // conditionName: undefined,
    conditionValue: true,
    dates: [],
    nums: [],
    values: [],
    judgeExistField: e?.judgeExistField,
    isPrefix: e.isPrefix,
    affixType: e.affixType,
    affixValue: !isEmpty(e.affixType)
      ? fieldAffix.value[e.affixType].value[fieldAffix.value[e.affixType].value.length - 1].value
      : undefined // 设置前缀的默认值
  }

  // 设置默认的筛选条件
  temp = {
    ...temp,
    ...searchConditionList.value[temp.fieldType].condition[0]
  }

  // 判读当前需不需要分数筛选
  if (props.isHaveScore) {
    temp.score = props.defaultScore
  }

  const updateData = cloneDeep(conditions.value)
  // 判断是新增还是编辑条件
  if (type === 'add') {
    updateData.push(temp)
    isShowAddSearchFieldBox.value = false
  } else {
    updateData[index as number] = temp
  }
  emit('update:conditions', updateData)
  console.log('updateData: ', updateData)
}
// 删除条件
function deleteIcon(index) {
  const updateData = cloneDeep(conditions.value)
  updateData.splice(index, 1)
  emit('update:conditions', updateData)
}

// 将搜索条件加工为接口所需的格式
function translateConditionsRawToSearchData() {
  try {
    const cloneData = cloneDeep(conditions.value)
    // 默认搜索条件，如果没有经营状态，则添加
    if (!some(cloneData, { fieldIndex: 'open_status' })) {
      cloneData.unshift({
        uuid: randomUUID(),
        isShowSearchFieldBox: false,
        fieldIndex: 'open_status',
        searchIndex: 'open_status',
        fieldName: '经营状态',
        fieldType: 'ENUM_1',
        path: ['基础信息', '工商信息', '经营状态'],
        conditionType: 'ANY',
        conditionValue: true,
        dates: [],
        nums: [],
        values: ['存续', '迁入', '迁出', '在业']
      })
    }
    const conditionsParams: Array<any> = []
    for (let index = 0; index < cloneData.length; index++) {
      const searchFormItem = cloneData[index]
      const pushConditionsData = {
        fieldIndex: searchFormItem.fieldIndex,
        searchIndex: searchFormItem.searchIndex,
        fieldName: searchFormItem.fieldName,
        fieldType: searchFormItem.fieldType,
        conditionType: searchFormItem.conditionType,
        conditionValue: searchFormItem.conditionValue,
        dates: searchFormItem.dates || [],
        nums: searchFormItem.nums || [],
        values: searchFormItem.values || [],
        ENUMValues: searchFormItem.values || [],
        unitValue: searchFormItem.unitValue,
        unitName: searchFormItem.unitName,
        isPrefix: searchFormItem.isPrefix,
        path: searchFormItem.path,
        affixType: searchFormItem.affixType,
        affixValue: searchFormItem.affixValue,
        judgeExistField: searchFormItem.judgeExistField
      }

      if (props.isHaveScore) {
        pushConditionsData['score'] = searchFormItem.score
      }

      if (searchFormItem.fieldType.includes('ENUM')) {
        // 枚举类型
        const keyEnumsData = searchEnumsList.value[searchFormItem.fieldIndex].value
        // 判断是不是联级选择器
        if (!isEmpty(keyEnumsData[0].children)) {
          const keyList: string[] = []
          searchFormItem.values.forEach(keyItem => {
            const key = keyItem[keyItem.length - 1]
            keyList.push(...findCascaderChildren(key, keyEnumsData))
          })
          pushConditionsData.values = uniq(keyList)
        }
      } else if (searchFormItem.fieldType.includes('NUM')) {
        // 数字类型
        const tempNum = isArray(pushConditionsData.nums) ? pushConditionsData.nums : [pushConditionsData.nums]
        for (let index = 0; index < tempNum.length; index++) {
          const element = tempNum[index]
          const unitValue = toNumber(searchFormItem.unitValue)
          if (unitValue) {
            tempNum[index] = element * unitValue
          }
        }
        pushConditionsData.nums = tempNum
      }

      conditionsParams.push(pushConditionsData)
    }

    console.log('conditionsParams: ', conditionsParams)
    return {
      conditions: conditionsParams
    }
  } catch (error) {
    console.error(error)
    return null
  }
}

// 将接口所需的格式还原为搜索条件
function translateSearchDataToConditionsRaw(data) {
  const cloneData = cloneDeep(data)
  const conditions = cloneData.map(item => {
    const data = {
      ...item,
      uuid: randomUUID()
    }
    if (item.fieldType.includes('ENUM')) {
      data.values = item.ENUMValues
      // // 枚举类型
      // const keyEnumsData = searchEnumsList.value[item.fieldIndex].value
      // // 判断是不是联级选择器
      // if (!isEmpty(keyEnumsData[0].children)) {
      //   const array = cloneDeep(item.values)
      //   data.values = findCascaderParent(array, keyEnumsData)
      //   console.log('1212', findCascaderParent(array, keyEnumsData))
      // }
    }
    if (item.fieldType === 'NUM_1' || item.fieldType === 'NUM_2' || item.fieldType === 'NUM_3') {
      // 数字类型
      data.nums = data.nums.map(numItem => (item.unitValue ? numItem / toNumber(item.unitValue) : numItem))
    }
    return data
  })
  console.log('conditions: ', conditions)
  return conditions
}

// 根据选中的父节点，获取该父级节点下的所有子节点
const findCascaderChildren = (key = '', arr = []) => {
  function getChildrenNode(nodeList) {
    const path: string[] = []
    for (let index = 0; index < nodeList.length; index++) {
      const { value, children = [] } = nodeList[index]
      path.push(value)
      if (children && children.length !== 0) {
        path.push(...getChildrenNode(children))
      }
    }

    return path
  }

  function deepFor(nodeList) {
    const path: string[] = []
    for (let index = 0; index < nodeList.length; index++) {
      const { value, children = [] } = nodeList[index]
      if (value === key) {
        path.push(value)
        if (children && children.length !== 0) {
          path.push(...getChildrenNode(children))
        }
        return path
      } else {
        const nodes = deepFor(children)
        if (children && children.length !== 0 && !isEmpty(nodes)) {
          path.push(...nodes)
          return path
        }
      }
    }
    return path
  }

  return deepFor(arr)
}

// 统计出现次数，给枚举类型添加上父节点
function findCascaderParent(keyList: string[], data = []) {
  const cloneKeyList = cloneDeep(keyList)
  function deepFor(nodeList: CascaderOptionType[]) {
    let tempKey: string[][] = []
    for (let index = 0; index < nodeList.length; index++) {
      const { label, value, children = [] } = nodeList[index]

      if (children?.length === 0) {
        if (cloneKeyList.includes(value as string)) {
          tempKey.push([value as string])
          const rmIndex = cloneKeyList.findIndex(n => n === value)
          console.log('rmIndex: ', rmIndex)
          if (rmIndex !== -1) {
            cloneKeyList.splice(rmIndex, 1)
          }
          console.log('keyList: ', cloneKeyList)
        }
      } else {
        const matchChildren = cloneDeep(deepFor(children))
        const parentRes: string[][] =
          matchChildren.length === children.length && cloneKeyList.includes(value as string)
            ? [[value as string]]
            : matchChildren.map(item => {
                item.unshift(value as string)
                return item
              })

        const rmIndex = cloneKeyList.findIndex(n => n === value)
        if (rmIndex !== -1) {
          cloneKeyList.splice(rmIndex, 1)
        }
        tempKey = tempKey.concat(parentRes)
      }
    }
    return tempKey
  }

  return deepFor(data)
}

// const findCascaderPath = (key = '', arr = []) => {
//   function deepFor(nodeList) {
//     const path: string[] = []
//     for (let index = 0; index < nodeList.length; index++) {
//       const { value, children = [] } = nodeList[index]
//       if (value === key) {
//         path.push(value)
//         return path
//       }

//       if (children.length !== 0 && !isEmpty(deepFor(children))) {
//         path.push(value, ...deepFor(children))
//         return path
//       }
//     }
//     console.log('path: ', path)
//     return path
//   }

//   return deepFor(arr)
// }

// 筛选条件变动
function handlerConditionChange(
  conditionType: string,
  options: { conditionName: string; conditionType: string; conditionValue?: boolean },
  fieldIndex: number
) {
  // 拷贝对象
  const tempFieldData = cloneDeep(conditions.value[fieldIndex])
  if (tempFieldData.fieldType.includes('DATE')) {
    // 如果是切换筛选条件类型且不为介于，对dates做只保留一位
    if (tempFieldData.dates.length === 2 && !['NBET', 'BET'].includes(conditionType)) {
      tempFieldData.dates = tempFieldData.dates.slice(0, 1)
    }
  } else if (tempFieldData.fieldType.includes('NUM') && !tempFieldData.fieldType.includes('ENUM')) {
    // 如果是切换筛选条件类型且不为介于，对dates做只保留一位
    if (tempFieldData.nums.length === 2 && !['NBET', 'BET'].includes(conditionType)) {
      tempFieldData.nums = tempFieldData.nums.slice(0, 1)
    }
  }
  console.log('tempFieldData: ', tempFieldData)

  conditions.value[fieldIndex] = { ...tempFieldData, ...options }
}

// 枚举值改变就立马触发规则校验
function handlerEnumChange(e: searchFormItemType, index: number) {
  if (props.areaLimit && e.searchIndex === 'ent_area') {
    searchFormRef.value?.validate([index])
  }
}

// 搜索
async function submit() {
  try {
    await searchFormRef.value?.validate()
    return translateConditionsRawToSearchData()
  } catch (error) {
    console.error(error)
    if (props.areaLimit) {
      error.errorFields.forEach(errItem => {
        const formItemData: searchFormItemType | undefined = error.values[errItem.name[0]]
        if (formItemData?.searchIndex === 'ent_area' && formItemData.values?.length > 50) {
          message.warning('所选省市最多可选择50个地区')
        }
      })
    }
    return Promise.reject(error)
  }
}

// 重置
function resetForm() {
  loading.value = true
  // searchForm.value = []
  // conditions.value = []
  emit('update:conditions', [])
  searchFormRef.value?.resetFields()
  nextTick(() => {
    // setDefaultSearchForm()
    loading.value = false
  })
}

// 点击消失条件筛选
function handlerClickOutside(type: 'add' | 'edit', index?) {
  console.log('handlerClickOutside')

  const item = conditions.value[index]
  if (type === 'edit') {
    conditions.value[index].isShowSearchFieldBox = false
  } else {
    isShowAddSearchFieldBox.value = false
  }
}

// onMounted(async () => {
//   try {
//     loading.value = true
//     const searchField = useSearchField()
//     console.log('searchField: ', searchField)

//     const result = await searchField.getSearchFieldData()

//     searchConditionList.value = searchField.getSearchConditionList
//     searchEnumsList.value = searchField.getSearchEnumsList
//     fieldList.value = searchField.getFieldList
//     fieldAffix.value = searchField.getFieldAffix

//     loading.value = false
//     isReady.value = true
//     emit('mountedDone')
//   } catch (error) {
//     loading.value = false
//     console.error(error)
//   }
// })

defineExpose({
  translateConditionsRawToSearchData,
  translateSearchDataToConditionsRaw,
  resetForm,
  submit,
  isReady
})
</script>

<style lang="less" scoped>
.searchBox {
  .searchFormList {
    position: relative;
    background-color: fade(#6553ee, 10%);
    padding: 20px;
    margin-bottom: 20px;
    &::before {
      width: 2px;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background-color: var(--g-primary-color, #6553ee);
      content: '';
    }

    .searchFormItem {
      display: flex;
      align-items: flex-start;

      .searchFieldBoxContainer {
        margin-right: 12px;
        display: inline-block;
        .searchFieldBox {
          z-index: 1100;
        }
      }

      .inputItem {
        flex: 1;
        margin-right: 12px;
      }

      :deep(.condition) {
        margin-right: 12px;
      }

      .score {
        margin-right: 12px;
      }

      .deleteIcon {
        font-size: 16px;
        line-height: 36px;
        visibility: hidden;
      }

      &:hover {
        .deleteIcon {
          visibility: inherit;
        }
      }
    }

    .searchFieldBoxContainer {
      position: relative;
      display: inline-block;
      .searchFieldBox {
        // position: absolute;
        // z-index: 1000;
        // margin-top: 8px;
      }
    }
  }

  .submitBtn {
    text-align: right;
  }
}
</style>
