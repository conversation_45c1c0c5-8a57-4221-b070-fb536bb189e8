/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-25 15:47:07
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 10:16:55
 * @FilePath: /corp-elf-web-consumer/src/components/layouts/headerMenu/menuList/index.tsx
 * @Description:
 */
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useKeepAliveCache } from '@/store'
import { Menu, MenuItem } from 'ant-design-vue'
import { MenuInfo } from 'ant-design-vue/es/menu/src/interface'
import { defineComponent, h, onMounted, watch } from 'vue'
import { ref } from 'vue'
import { useRoute, useRouter, RouteRecordRaw } from 'vue-router'
import './styles.less' // 引入外部样式
import { asyncRouter } from '@/router/routes'

const { SubMenu } = Menu

export default defineComponent({
  components: { iconfontIcon, SubMenu },
  setup() {
    const route = useRout<PERSON>()
    const router = useRouter()
    const keepAliveCacheStore = useKeepAliveCache()

    // 当前高亮选项
    const menuSelectedKeys = ref<string[]>([])

    // menu的渲染方法
    const renderMenuText = (menu: RouteRecordRaw) => (
      <div class="relative color-#222 hoverPrimaryColor">
        <span class="fw-500 fs-16px">{menu.meta?.title}</span>
        {menu.children?.find(item => !item.meta?.hidden) ? <iconfontIcon icon="icon-chevron-down" /> : null}
      </div>
    )
    const renderSubMenu = (menu: RouteRecordRaw) => (
      <SubMenu key={menu.path}>
        {{
          title: () => renderMenuText(menu),
          default: () => menu.children?.filter(item => !item.meta?.hidden).map(subMenu => renderMenuItem(subMenu))
        }}
      </SubMenu>
    )
    const renderMenuItem = (menu: RouteRecordRaw) => (
      <MenuItem key={menu.path}>
        {{
          title: () => menu.meta?.title,
          default: () => renderMenuText(menu)
        }}
      </MenuItem>
    )

    const renderMenuTree =
      // permissionStore.permissionList
      asyncRouter
        .filter(item => !item.meta?.hidden && !['dashboard', 'system'].includes(item.name as string)) // 过滤隐藏的
        .map(item => {
          const temp = item.children && !item.meta?.polymerization ? renderSubMenu(item) : renderMenuItem(item)
          return h(temp, { class: 'px32px!' })
        })

    /**
     * @description: 菜单点击事件
     * @param {*} path
     * @return {*}
     */
    function menuSelect(menuItem: MenuInfo) {
      router.push(menuItem.key as string).finally(() => {
        setMenuSelectKey()
        keepAliveCacheStore.delOthersCachedViews(route)
      })
    }

    function setMenuSelectKey() {
      const path = route.fullPath
      const temp: string[] = []
      for (let i = 1; i <= path.length; i++) {
        let subPath = path.substring(0, i)
        if (path[i] === '/' || i === path.length) {
          temp.push(subPath)
        }
      }
      menuSelectedKeys.value = temp
    }

    watch(
      () => route.fullPath,
      () => setMenuSelectKey()
    )

    onMounted(() => {
      setMenuSelectKey()
    })

    return () => (
      <Menu
        selectedKeys={menuSelectedKeys.value}
        mode="horizontal"
        class="menuList background-#ffffff00! border-width-0px!"
        onClick={menuSelect}
      >
        {renderMenuTree}
      </Menu>
    )
  }
})
