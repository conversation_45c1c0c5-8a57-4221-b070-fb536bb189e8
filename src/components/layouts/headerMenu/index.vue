<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-10 16:00:21
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-19 14:43:45
 * @FilePath: /corp-elf-web-consumer/src/components/layouts/headerMenu/index.vue
 * @Description: 
-->
<template>
  <div
    :style="{ backgroundColor: `rgba(255,255,255,${transparency})` }"
    :class="['headerMenu sticky t-0 w100% z-20', transparency > 0.5 ? 'headerMenu-box-shadow' : '']"
  >
    <a-layout-header :style="{ backgroundColor: `rgba(255,255,255,${transparency})` }" class="p0!">
      <div class="flex items-center max-w1400px m-0 m-x-auto">
        <logo />
        <MenuLIst class="min-w-100px flex-1" />
        <div class="flex items-center">
          <SearchBox class="mr-12px" />
          <router-link to="/findCompany/advanceSearch?type=search" class="fs-16 color-#222">高级搜索</router-link>
          <a-divider type="vertical" style="width: 2px; background-color: #8181816e; margin: 0 12px" />
          <div class="fs-16 color-#F7B501 hover:(cursor-pointer color-#ffd45c)" @click="vipModalRef?.openModal()">
            <CrownOutlined class="fs-20px mr-4px" />
            <span>{{ !userStore.isVip ? '开通' : '' }}会员</span>
          </div>
          <a-divider type="vertical" style="width: 2px; background-color: #8181816e; margin: 0 12px" />
          <UserMenu />
        </div>
      </div>
    </a-layout-header>

    <vipModal ref="vipModalRef" />
  </div>
</template>

<script setup lang="ts" name="headerMenu">
import UserMenu from './userMenu/index.vue'
import logo from './logo.vue'
import { computed, ref } from 'vue'
import { useEventBus, useWindowScroll } from '@vueuse/core'
import { round } from 'lodash-es'
import SearchBox from './searchBox.vue'
import MenuLIst from './menuList/index'
import { CrownOutlined } from '@ant-design/icons-vue'
import vipModal from './vipModal/index.vue'
import { useUserStore } from '@/store'

const userStore = useUserStore()
const vipModalRef = ref<InstanceType<typeof vipModal>>()

// 计算滚动距离，动态设置透明度
const { y } = useWindowScroll()
const MAX_SCROLL = 30
const transparency = computed(() => round(y.value / MAX_SCROLL, 3))

const bus = useEventBus<string>('openVipModal')
bus.on(() => {
  vipModalRef.value?.openModal()
})
</script>

<style lang="less" scoped>
.headerMenu-box-shadow {
  // box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  box-shadow: 0 0 12px #ddd;
}
</style>
