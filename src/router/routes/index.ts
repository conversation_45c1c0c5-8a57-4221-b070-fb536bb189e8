/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-09 15:59:34
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 10:50:46
 * @FilePath: /corp-elf-web-consumer/src/router/routes/index.ts
 * @Description:
 */

import { RouteRecordRaw } from 'vue-router'

const basicRouterMap: RouteRecordRaw[] = [
  // {
  //   name: 'overview',
  //   path: '/overview',
  //   component: () => import('@/views/overview/index.vue'),
  //   meta: { title: '概览', hidden: true, keepAlive: false }
  // },
  {
    name: 'term',
    path: '/term',
    redirect: '/term/privacyPolicy',
    component: () => import('@/components/layouts/TermLayout.vue'),
    meta: { title: '协议', hidden: true },
    children: [
      {
        name: 'privacy-policy',
        path: '/term/privacyPolicy',
        component: () => import('@/views/term/privacyPolicy.vue'),
        meta: { title: '用户服务协议' }
      },
      {
        name: 'user-agreement',
        path: '/term/userAgreement',
        component: () => import('@/views/term/userAgreement.vue'),
        meta: { title: '隐私政策' }
      },
      {
        name: 'user-membershipAgreement',
        path: '/term/membershipAgreement',
        component: () => import('@/views/term/membershipAgreement.vue'),
        meta: { title: '会员服务协议' }
      }
    ]
  },
  { name: 'register', path: '/register', redirect: '/', meta: { title: '分享注册', hidden: true } },
  { name: '404', path: '/404', component: () => import('@/views/error/404.vue'), meta: { title: '404', hidden: true } }
]

const asyncRouter: RouteRecordRaw[] = [
  {
    name: 'home',
    path: '/home',
    redirect: '/home/<USER>',
    component: () => import('@/components/layouts/RouteView.vue'),
    meta: { title: '我的', polymerization: true },
    children: [
      {
        name: 'home-index',
        path: '/home/<USER>',
        component: () => import('@/views/home/<USER>'),
        meta: { title: '我的概况', hidden: true }
      }
    ]
  },
  {
    name: 'find-company',
    path: '/findCompany',
    redirect: '/findCompany/index',
    component: () => import('@/components/layouts/RouteView.vue'),
    meta: { title: '找企业', polymerization: true },
    children: [
      {
        name: 'find-company-index',
        path: '/findCompany/index',
        component: () => import('@/views/findCompany/index.vue'),
        meta: { title: '找企业概况', hidden: true },
        children: [
          {
            name: 'find-company-advanceSearch',
            path: '/findCompany/advanceSearch',
            component: () => import('@/views/search/advance/index.vue'),
            meta: { title: '找企业高级搜索', hidden: true, name: '/findCompany/index' }
          },
          {
            name: 'find-company-myModel',
            path: '/findCompany/myModel',
            component: () => import('@/views/findCompany/components/modelTable/index.vue'),
            meta: { title: '找企业我的模型', hidden: true, name: '/findCompany/index' }
          },
          {
            name: 'find-company-followCompany',
            path: '/findCompany/followCompany',
            component: () => import('@/views/followList/components/followCompanyTable.vue'),
            meta: { title: '找企业关注企业', hidden: true, name: '/findCompany/index' }
          },
          {
            name: 'find-company-ignore',
            path: '/findCompany/ignore',
            component: () => import('@/views/findCompany/components/modelTable/index.vue'),
            meta: { title: '找企业忽略企业', hidden: true, name: '/findCompany/index' }
          }
        ]
      }
    ]
  },
  {
    name: 'executiveComments',
    path: '/executiveComments',
    redirect: '/executiveComments/index',
    component: () => import('@/components/layouts/RouteView.vue'),
    meta: { title: '高管说', polymerization: true },
    children: [
      {
        name: 'executiveComments-index',
        path: '/executiveComments/index',
        component: () => import('@/views/executiveComments/list/index.vue'),
        meta: { title: '高管说概况', hidden: true, icon: '' }
      },
      {
        name: 'executiveComments-detail',
        path: '/executiveComments/detail',
        component: () => import('@/views/executiveComments/detail/index.vue'),
        meta: { title: '高管详情', hidden: true, icon: '' }
      },
      {
        name: 'executiveComments-interpret',
        path: '/executiveComments/interpret',
        component: () => import('@/views/executiveComments/interpret/index.vue'),
        meta: { title: '高管解读', hidden: true, icon: '' }
      }
    ]
  },
  {
    name: 'circleLayer',
    path: '/circleLayer',
    redirect: '/circleLayer/overview',
    component: () => import('@/components/layouts/RouteView.vue'),
    meta: { title: 'ToB圈子', icon: 'icon-shouye-hangye', polymerization: true },
    children: [
      {
        name: 'circleLayer-overview',
        path: '/circleLayer/overview',
        component: () => import('@/views/circleLayer/overview/index.vue'),
        meta: { title: '概况', icon: '', hidden: true }
      },
      {
        name: 'circleLayer-myself',
        path: '/circleLayer/myself',
        component: () => import('@/views/circleLayer/myself/index.vue'),
        meta: { title: '我的圈层', icon: '', hidden: true }
      },
      {
        name: 'circleLayer-detail',
        path: '/circleLayer/detail',
        component: () => import('@/views/circleLayer/detail/index.vue'),
        meta: { title: '圈层详情', icon: '', hidden: true }
      }
    ]
  },
  {
    name: 'industry-insights',
    path: '/industryInsights',
    redirect: '/industryInsights/overview',
    component: () => import('@/components/layouts/RouteView.vue'),
    meta: { title: '行业洞察', polymerization: true },
    children: [
      {
        name: 'industry-insights-overview',
        path: '/industryInsights/overview',
        component: () => import('@/views/industryInsights/index.vue'),
        meta: { title: '行业洞察', hidden: true }
      }
    ]
  },
  {
    name: 'follow-overview',
    path: '/followOverview',
    redirect: '/followOverview/index',
    component: () => import('@/components/layouts/RouteView.vue'),
    meta: { title: '关注', polymerization: true, hidden: true },
    children: [
      {
        name: 'follow-overview-index',
        path: '/followOverview/index',
        redirect: '/followOverview/company',
        component: () => import('@/views/followList/index.vue'),
        meta: { title: '关注列表', hidden: true, icon: '' },
        children: [
          {
            name: 'follow-overview-company',
            path: '/followOverview/company',
            component: () => import('@/views/followList/components/followCompanyTable.vue'),
            meta: { title: '关注的企业', hidden: true, icon: '', name: '/followOverview/index' }
          },
          {
            name: 'follow-overview-executives',
            path: '/followOverview/executives',
            component: () => import('@/views/followList/components/followExecutivesTable.vue'),
            meta: { title: '关注的高管', hidden: true, icon: '', name: '/followOverview/index' }
          },
          {
            name: 'follow-overview-circle',
            path: '/followOverview/circle',
            component: () => import('@/views/followList/components/followCircleTable.vue'),
            meta: { title: '关注的圈子', hidden: true, icon: '', name: '/followOverview/index' }
          }
        ]
      }
    ]
  },
  {
    name: 'more',
    path: '/more',
    redirect: '/more/overview',
    component: () => import('@/components/layouts/RouteView.vue'),
    meta: { title: '更多' },
    children: [
      {
        name: 'dataStore-index',
        path: '/dataStore/index',
        component: () => import('@/views/dataStore/overview/index.vue'),
        meta: { title: '数据商店', icon: '' }
      },
      {
        name: 'dataStore-detail',
        path: '/dataStore/detail',
        component: () => import('@/views/dataStore/detail/index.vue'),
        meta: { title: '数据商店详情', hidden: true, icon: '' }
      },
      {
        name: 'more-overview',
        path: '/more/overview',
        component: () => import('@/views/more/overview/index.vue'),
        meta: { title: '帮助' }
      },
      {
        name: 'more-bengine',
        path: '/more/bengine',
        component: () => import('@/views/more/bengine/index.vue'),
        meta: { title: '去官网' }
      },
      {
        name: 'more-video',
        path: '/more/video',
        component: () => import('@/views/more/video/index.vue'),
        meta: { title: '视频详情', hidden: true, keepAlive: false }
      }
    ]
  },
  {
    name: 'companyInfo',
    path: '/companyInfo',
    redirect: '/companyInfo/index',
    component: () => import('@/components/layouts/RouteView.vue'),
    meta: { title: '企业', hidden: true },
    children: [
      {
        name: 'companyInfo-index',
        path: '/companyInfo/index',
        component: () => import('@/views/companyInfo/index.vue'),
        meta: { title: '企业详情', hidden: true, icon: '' }
      }
    ]
  },
  // {
  //   name: 'search',
  //   path: '/search',
  //   redirect: '/search/advance',
  //   component: () => import('@/components/layouts/RouteView.vue'),
  //   meta: { title: '企业库', hidden: true },
  //   children: [
  //     {
  //       name: 'search-advance',
  //       path: '/search/advance',
  //       component: () => import('@/views/search/advance/index.vue'),
  //       meta: { title: '企业库', hidden: true, icon: '' }
  //     }
  //   ]
  // },
  {
    name: 'invite',
    path: '/invite',
    redirect: '/invite/home',
    component: () => import('@/components/layouts/RouteView.vue'),
    meta: { title: '推荐有礼', hidden: true },
    children: [
      {
        name: 'invite-home',
        path: '/invite/home',
        component: () => import('@/views/invite/index.vue'),
        meta: { title: '推荐有礼', hidden: true, icon: '' }
      }
    ]
  },
  {
    name: 'dynamics',
    path: '/dynamics',
    redirect: '/dynamics/index',
    component: () => import('@/components/layouts/RouteView.vue'),
    meta: { title: '企业动态', hidden: true },
    children: [
      {
        name: 'dynamics-index',
        path: '/dynamics/index',
        component: () => import('@/views/dynamics/index.vue'),
        meta: { title: '企业动态2', hidden: true, icon: '' }
      }
    ]
  }
]

const NOT_FOUND_ROUTE = {
  name: 'NotFound',
  path: '/:pathMatch(.*)*',
  redirect: '/404',
  hidden: true
}

export { basicRouterMap, asyncRouter, NOT_FOUND_ROUTE }
