/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-09 15:58:03
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 10:20:37
 * @FilePath: /corp-elf-web-consumer/src/router/index.ts
 * @Description:
 */
import { App } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { basicRouterMap, asyncRouter, NOT_FOUND_ROUTE } from './routes'
import { setupRouterGuard } from './guard'
import { isEmpty, isNull } from 'lodash-es'

export const router = createRouter({
  history: createWebHistory('/'),
  // routes,
  routes: [
    {
      path: '/',
      name: 'main',
      component: () => import('@/components/layouts/index.vue'),
      redirect: '/home',
      children: asyncRouter
    },
    ...basicRouterMap,
    NOT_FOUND_ROUTE
  ],
  // scrollBehavior: () => ({ left: 0, top: 0 })
  scrollBehavior(_to, _from, savedPosition) {
    // console.log(savedPosition);//记录滚动条高度
    if (!isNull(savedPosition)) {
      return savedPosition
    } else if (!isEmpty(_to.hash)) {
      // 有锚点，不操作
    } else {
      return { top: 0 }
    }
  }
})

export function setupRouter(app: App) {
  app.use(router)
  setupRouterGuard(router)
}
