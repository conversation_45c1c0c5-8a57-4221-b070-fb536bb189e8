/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-11 15:50:23
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 10:43:01
 * @FilePath: /corp-elf-web-consumer/src/router/guard/permission.ts
 * @Description:
 */
import type { Router } from 'vue-router'
import { useUserStore } from '@/store'
import { isEmpty } from 'lodash-es'
import NProgress from 'nprogress'
import showLoginModal from '@/components/loginModal'
import { UAParser } from 'ua-parser-js'

// 访问白名单
const WHITE_ROUTE_LIST = [
  '/404',
  '/overview',
  '/term',
  '/term/privacyPolicy',
  '/term/userAgreement',
  '/term/membershipAgreement',
  '/dataStore',
  '/dataStore/index',
  '/dataStore/detail',
  '/more',
  '/more/overview',
  '/more/video',
  '/more/bengine'
]

const parser = new UAParser()
const { browser, device } = parser.getResult()
const isWechat = () => browser.name === 'WeChat' && device.type === 'mobile'

export function createPermissionGuard(router: Router) {
  const userStore = useUserStore()
  // const permissionStore = usePermissionStore()
  router.beforeEach(async (to, from) => {
    NProgress.start()
    const token = userStore.getToken

    // 先判断有没有路由权限点，没有就添加
    // if (permissionStore.addRouters.length == 0) {
    //   const asyncRouter = await userStore.fetchPermissionList() // 获取用户的权限点
    userStore.getUserMemberInfo() // 获取用户会员信息
    //   asyncRouter.forEach(routerItem => router.addRoute(routerItem))
    //   router.addRoute(NOT_FOUND_ROUTE)
    //   return { ...to, replace: true }
    // }

    // // 先判断有没有token
    // if (isEmpty(token)) {
    //   // 没有，判断是不是前往白名单路由
    //   if (!WHITE_ROUTE_LIST.includes(to.path)) {
    //     // 判断来自页面是不是白名单
    //     if (WHITE_ROUTE_LIST.includes(from.path)) {
    //       // 弹窗，停止导航
    //       const registerCode = from.query.registerCode
    //       if (!isWechat()) {
    //         showLoginModal()
    //       } else {
    //         // 在微信内点击链接，跳转小程序处理
    //         let href = `weixin://dl/business/?appid=wx4d86b574b94ddcf1&path=pages/insight/index`
    //         if (registerCode) {
    //           const queryParams = encodeURIComponent(`registerCode=${registerCode}&from=register`)
    //           href += `&query=${queryParams}`
    //         }
    //         window.open(href, '_blank')
    //       }

    //       return false
    //     }
    //     // 不是白名单路由导航去overview
    //     return { name: 'overview' }
    //   }
    // } else if (to.path === '/overview') {
    //   // 判断前往的路由是什么，如果是overview跳转到home，其他正常导航
    //   return { name: 'home' }
    // }
  })

  router.afterEach(() => {
    NProgress.done()
  })
}
