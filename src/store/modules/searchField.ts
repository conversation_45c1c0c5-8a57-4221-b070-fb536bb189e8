/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-27 16:44:18
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 17:19:26
 * @FilePath: /corp-elf-web-consumer/src/store/modules/searchField.ts
 * @Description:
 */
import { companySearchCondition, companySearchEnums, companySearchField, companySearchFieldAffix } from '@/api/api'
import useRequest from '@/hooks/useRequest'
import { isEmpty } from 'lodash-es'
import { defineStore } from 'pinia'

export const useSearchField = defineStore('searchField', () => {
  // const searchConditionList = {}
  // const searchEnumsList = {}
  // const fieldList = {}
  // const fieldAffix = {}

  const { dataList: searchConditionList } = useRequest(companySearchCondition)
  const { dataList: searchEnumsList } = useRequest(companySearchEnums)
  const { dataList: fieldList } = useRequest(companySearchField)
  const { dataList: fieldAffix } = useRequest(companySearchFieldAffix)

  return {
    searchConditionList,
    searchEnumsList,
    fieldList,
    fieldAffix
  }
  // getters: {
  //   getSearchConditionList: state => state.searchConditionList,
  //   getSearchEnumsList: state => state.searchEnumsList,
  //   getFieldList: state => state.fieldList,
  //   getFieldAffix: state => state.fieldAffix
  // },
  // actions: {
  //   // 获取条件
  //   async setCompanySearchCondition() {
  //     try {
  //       if (isEmpty(this.searchConditionList)) {
  //         const { result } = await companySearchCondition()
  //         this.searchConditionList = result
  //       }

  //       return this.searchConditionList
  //     } catch (error) {
  //       console.error(error)
  //     }
  //   },
  //   // 获取枚举类型
  //   async setCompanySearchEnums() {
  //     try {
  //       if (isEmpty(this.searchEnumsList)) {
  //         const { result } = await companySearchEnums()
  //         this.searchEnumsList = result
  //       }

  //       return this.searchEnumsList
  //     } catch (error) {
  //       console.error(error)
  //     }
  //   },
  //   // 获取所有字段列表
  //   async setFieldList() {
  //     try {
  //       if (isEmpty(this.fieldList)) {
  //         const { result } = await companySearchField()
  //         this.fieldList = result
  //       }

  //       return this.fieldList
  //     } catch (error) {
  //       console.error(error)
  //     }
  //   },
  //   // 获取字段前缀
  //   async setCompanySearchFieldAffix() {
  //     try {
  //       if (isEmpty(this.fieldAffix)) {
  //         const { result } = await companySearchFieldAffix()
  //         this.fieldAffix = result
  //       }

  //       return this.fieldAffix
  //     } catch (error) {
  //       console.error(error)
  //     }
  //   },
  //   async getSearchFieldData() {
  //     try {
  //       await Promise.all([
  //         this.setCompanySearchCondition(),
  //         this.setCompanySearchEnums(),
  //         this.setFieldList(),
  //         this.setCompanySearchFieldAffix()
  //       ])
  //       return {
  //         searchConditionList: this.searchConditionList,
  //         searchEnumsList: this.searchEnumsList,
  //         fieldList: this.fieldList,
  //         fieldAffix: this.fieldAffix
  //       }
  //     } catch (error) {
  //       console.error(error)
  //     }
  //   }
  // }
})
