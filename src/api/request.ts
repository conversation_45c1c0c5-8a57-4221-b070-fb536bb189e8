/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-07-30 14:45:33
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-13 11:34:09
 * @FilePath: /corp-elf-web-consumer/src/api/request.ts
 * @Description:
 */
import type { ErrorMessageType, ResponseData } from '~/types/response'
import axios, { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { Modal, notification } from 'ant-design-vue'
import { useUserStore } from './../store/modules/user'
import { getToken } from '@/utils/auth/token'
import { isEmpty, cloneDeep } from 'lodash-es'
import { h } from 'vue'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useEventBus } from '@vueuse/core'

// 引入vip弹窗
const { emit: openVipModal } = useEventBus('openVipModal')

// 白名单接口不参与网关转发和响应拦截器
const WHITE_URL = ['/executeSaidExplain/chatStream']

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_BASE_API,
  timeout: 1000 * 60 // 请求超时时间
})

// 请求拦截-基础方法
const baseRequestInterceptors = {
  onFulfilled: (config: InternalAxiosRequestConfig) => {
    const token = getToken() as string

    // 设置Header
    const mergeHeader: { 'X-Access-Token'?: string; 'PLATFORM-TYPE': 'LITE' | 'MP' } = {
      'PLATFORM-TYPE': 'LITE'
    }
    if (token) {
      mergeHeader['X-Access-Token'] = token
    }
    config.headers = Object.assign(config.headers, mergeHeader)

    // get请求添加上时间戳
    if (config.method == 'get') {
      config.params = {
        _t: new Date().valueOf(),
        ...config.params
      }
    }

    return config
  },
  onRejected: (error: AxiosError) => {
    notification.error({ message: '系统提示', description: error.message })
    return Promise.reject(error)
  }
}

// 请求拦截-转换为请求网关
const gatewayInterceptors = (config: InternalAxiosRequestConfig) => {
  if (WHITE_URL.includes(config.url!)) {
    return config
  }

  const gatewayConfig = cloneDeep(config)
  const gatewayParams = {
    mtop: gatewayConfig.url,
    method: gatewayConfig.method?.toLocaleUpperCase(),
    serviceId: 'corp_elf',
    body: isEmpty(gatewayConfig.data) ? null : gatewayConfig.data,
    param: isEmpty(gatewayConfig.params) ? null : gatewayConfig.params
  }
  config = {
    ...config,
    data: gatewayParams,
    method: 'POST',
    params: {},
    url: '/context/corpElf'
  }

  return config
}

// axios请求拦截器
service.interceptors.request.use(baseRequestInterceptors.onFulfilled, baseRequestInterceptors.onRejected)
// 将请求转发去网关地址
if (['production', 'staging'].includes(import.meta.env.MODE)) {
  // 线上、测试环境
  service.interceptors.request.use(gatewayInterceptors)
} else {
  // 本地环境，自行决定是否打开网关
  service.interceptors.request.use(gatewayInterceptors)
}

// axios响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<any, ResponseData>) => {
    const userStore = useUserStore()
    const token = userStore.token
    const res = response.data
    const whiteCode = [
      // 正常状态码
      ...[0, 200, 'SUCCESS'],
      // 微信二维码绑定用
      ...['NON_BIDDING', 'WAITING']
    ]

    if (WHITE_URL.includes(response.config.url!)) {
      return res
    }
    // 根据自定义错误码判断请求是否成功
    if (!whiteCode.includes(res.code)) {
      if (token && res.code === 'NON_AUTH') {
        // 登录已过期
        Modal.error({
          title: '登录已过期',
          content: res.message,
          okText: '重新登录',
          mask: false,
          onOk: () => {
            userStore.logout()
          }
        })
      } else if (token && res.code === 'PAGE_AUTH_ERROR') {
        // 无翻页权限
        notification.warning({ message: '系统提示', description: res.message || res.msg })
      } else if (token && res.code === 'MEMBER_REQUEST_LIMIT') {
        Modal.confirm({
          title: '没有剩余提问次数',
          content: res.message,
          icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
          autoFocusButton: 'ok',
          okText: '开通会员',
          cancelText: '关闭',
          onCancel() {},
          onOk: () => {
            openVipModal()
          }
        })
      } else {
        // 错误上报
        // Sentry.captureException(res)
        // 普通报错提示
        notification.error({ message: '系统提示', description: res.message || res.msg })
      }
      return Promise.reject(new Error(res.message || res.msg || 'Error'))
    }
    return res
  },
  (err: AxiosError) => {
    // 处理 HTTP 网络错误
    const errorMessage: ErrorMessageType = {
      config: err,
      requestUrl: err.config?.url || '',
      response: undefined,
      message: ''
    }

    if (err.response) {
      const userStore = useUserStore()
      const token = userStore.token
      const data = err.response.data as ResponseData

      // 定义错误返回数据
      errorMessage.response = data
      errorMessage.message = data.message

      switch (err.response.status) {
        case 403:
          notification.error({ message: '系统提示', description: '拒绝访问', duration: 4 })
          break
        case 500:
          // notification.error({ message: '系统提示', description:'Token失效，请重新登录!',duration: 4})
          if (token && data.code === 'NON_AUTH') {
            Modal.error({
              title: '登录已过期',
              content: data.message,
              okText: '重新登录',
              mask: false,
              onOk: () => {
                userStore.logout()
              }
            })
          }
          break
        case 404:
          notification.error({ message: '系统提示', description: '很抱歉，资源未找到!', duration: 4 })
          break
        case 504:
          notification.error({ message: '系统提示', description: '网络超时' })
          break
        case 401:
          notification.error({ message: '系统提示', description: '未授权，请重新登录', duration: 4 })
          if (token) {
            userStore.logout()
          }
          break
        case 400:
          notification.error({ message: '系统提示', description: '加载资源失败' })
          // Sentry.captureException(err)
          break
        default:
          // 错误上报
          // Sentry.captureException(err)
          notification.error({
            message: '系统提示',
            description: data.message,
            duration: 4
          })
          break
      }
    }

    return Promise.reject(errorMessage)
  }
)

export default service
