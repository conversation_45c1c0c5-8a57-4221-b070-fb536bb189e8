<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-06 16:19:27
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-18 16:23:42
 * @FilePath: /corp-elf-web-consumer/src/views/findCompany/index.vue
 * @Description: 
-->
<template>
  <a-row :gutter="16" :wrap="false">
    <a-col flex="256px">
      <a-affix :offset-top="80">
        <ConditionAside />
      </a-affix>
    </a-col>

    <a-col flex="auto">
      <router-view v-slot="{ Component, route }">
        <component :is="Component" v-bind="propsParams" :key="`${route.query.type}_${new Date().valueOf()}`" />
      </router-view>
    </a-col>
  </a-row>
</template>

<script lang="ts">
export default defineComponent({
  beforeRouteEnter: async (to, _form) => {
    // 判断有没有前去的type类型
    if (!to.query.type) {
      // 如果没有则判断有没有自定义模型，有则默认进入第一个模型结果列表。如果没有模型，默认进入高级搜索页面。
      const { result } = await companyShowSearchConditionV2({ type: 'RECOMMEND_SEARCH', version: 'V_3_0' })
      return result.records.length === 0
        ? { path: '/findCompany/advanceSearch', query: { type: 'search' } }
        : { path: '/findCompany/myModel', query: { type: 'model', id: result.records[0].id } }
    }
  }
})
</script>

<script setup lang="ts" name="find-company-overview">
import ConditionAside from './components/conditionAside/index.vue'
import { useRoute } from 'vue-router'
import { computed, defineComponent } from 'vue'
import { pick } from 'lodash-es'
import { companyShowSearchConditionV2 } from '@/api/api'

const route = useRoute()
const propsParams = computed(() => {
  return pick(route.query, ['id', 'collectId'])
})
</script>

<style scoped></style>
