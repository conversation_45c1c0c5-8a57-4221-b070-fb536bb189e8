<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-11 11:41:02
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-17 18:51:43
 * @FilePath: /corp-elf-web-consumer/src/views/home/<USER>/navCard.vue
 * @Description: 
-->
<template>
  <sideNavMenu
    :navMenu="[
      { label: '我的商瞳', key: '我的商瞳', icon: 'icon-faxian' },
      { label: '关注列表', key: '关注列表', icon: 'icon-chart-bubble' },
      {
        label: '高管',
        key: '高管',
        icon: 'icon-chart-bubble',
        children: [{ label: 'AI高管解读', key: 'AI高管解读', icon: 'icon-point' }]
      },
      {
        label: '企业',
        key: '企业',
        icon: 'icon-chart-bubble',
        children: [
          { label: '找企业', key: '找企业', icon: 'icon-point' },
          { label: '我保存的搜索项', key: '我保存的搜索项', icon: 'icon-point' },
          { label: '我的企业模型', key: '我的企业模型', icon: 'icon-point' }
        ]
      }
    ]"
    @click="handleSideNavMenuChange"
  >
    <template #titleText="navItem">
      <span :class="navItem.key === '关注列表' ? 'hoverPrimaryColor' : ''">{{ navItem.label }}</span>
    </template>
    <template #titleIcon="navItem">
      <iconfontIcon :icon="navItem.icon" :extraCommonProps="{ class: [navItem.key === '我的商瞳' ? 'fs-30px!' : 'fs-24px!'] }" />
    </template>
    <template #childrenText="item">
      <template v-if="item.key === '我的企业模型'">
        {{ item.label }}
        <span class="ml-4px color-red fs-12px">New</span>
      </template>
      <template v-else>{{ item.label }}</template>
    </template>
  </sideNavMenu>
</template>

<script setup lang="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useRouter } from 'vue-router'
import { executiveRelationList, executeSaidExplainLast, companyShowSearchConditionV2 } from '@/api/api'
import { Modal } from 'ant-design-vue'
import { h } from 'vue'
import useListLoading from '@/hooks/useListLoading'
import nProgress from 'nprogress'
import sideNavMenu, { BaseNavItem } from '@/components/tools/sideNavMenu'

const router = useRouter()

// 打开高管解读界面
async function openInterpret() {
  try {
    nProgress.start()
    // 判断是否有关注的高管
    let followTotal = 0
    const { result: rollowRes } = await executiveRelationList({})
    followTotal = rollowRes.total
    if (followTotal === 0) {
      // 关注数为0，不给进入，提示关注高管
      Modal.info({
        title: '提示',
        content: '请先关注高管',
        icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
        autoFocusButton: 'ok',
        onCancel() {},
        onOk: () => {}
      })
      nProgress.done()
      return false
    }

    // 判断之前有没有历史对话记录
    const { result: lastInterpretRes } = await executeSaidExplainLast()
    console.log('result: ', lastInterpretRes)
    nProgress.done()
    router.push({
      path: '/executiveComments/interpret',
      query: {
        executiveId: lastInterpretRes.executiveId,
        executiveName: lastInterpretRes.executiveName
      }
    })
  } catch (error) {
    nProgress.done()
    console.log(error)
  }
}

// 获取自定义条件列表
const { dataList: modelList, getData: getModelList } = useListLoading(
  companyShowSearchConditionV2,
  { type: 'RECOMMEND_SEARCH', version: 'V_3_0' },
  { pageParams: { pageSize: 1000 }, immediateReqData: false }
)

async function openMyModel() {
  try {
    nProgress.start()
    // 判断是否有过自定义模型
    await getModelList()
    console.log('modelList.value: ', modelList.value)
    nProgress.done()
    // 如果有自定义模型，跳转到自定义模型，没有就打开添加模型弹窗
    modelList.value.length !== 0
      ? router.push({ path: `/findCompany/index`, query: { type: 'model', id: modelList.value[0].id } })
      : router.push({ path: '/findCompany/index', query: { type: 'model', activity: 'openCustomModel' } })
  } catch (error) {
    nProgress.done()
    console.error(error)
  }
}

function handleSideNavMenuChange(navItem: BaseNavItem) {
  console.log('navItem: ', navItem)
  switch (navItem.key) {
    case '关注列表':
      router.push({ path: '/followOverview/index' })
      break
    case 'AI高管解读':
      openInterpret()
      break
    case '找企业':
      router.push({ path: '/findCompany/index' })
      break
    case '我保存的搜索项':
      router.push({ path: '/findCompany/index', query: { type: 'search', activity: 'openSavedSearch' } })
      break
    case '我的企业模型':
      openMyModel()
      break

    default:
      break
  }
}
</script>

<style scoped lang="less">
.navList {
  line-height: 2;
}

.interpretColor {
  background: linear-gradient(90deg, #116af0, #eb0050);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
