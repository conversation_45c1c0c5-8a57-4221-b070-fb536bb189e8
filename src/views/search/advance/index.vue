<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-11 17:43:06
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-18 18:18:07
 * @FilePath: /corp-elf-web-consumer/src/views/search/advance/index.vue
 * @Description: 
-->
<template>
  <div class="advance">
    <a-card title="筛选条件">
      <div class="saveConditionBox">
        <a-radio-group v-model:value="searchForm.isAnd" button-style="solid">
          <a-radio-button :value="true">且</a-radio-button>
          <a-radio-button :value="false">或</a-radio-button>
        </a-radio-group>

        <div class="condition">
          <saveConditionListModal ref="saveConditionListDialogRef" @searchCompany="handlerConditionListClick" />
          <a-button @click="saveCondition" :disabled="searchForm.conditions.length === 0"> 保存条件 </a-button>
        </div>
      </div>

      <searchBox ref="searchBoxRef" v-model:conditions="searchForm.conditions" @mountedDone="handlerMountedDone" />

      <div class="submitBtn">
        <a-space>
          <a-button @click="resetForm">重置</a-button>
          <a-button type="primary" @click="handlerSubmit" :disabled="searchForm.conditions.length === 0"> 查询 </a-button>
        </a-space>
      </div>
    </a-card>

    <companyList ref="companyListRef" class="companyList" :searchForm="companyListSearchForm" />
    <saveConditionModal ref="saveConditionModalRef" @refreshConditionList="handlerRefreshConditionList" />
  </div>
</template>

<script setup lang="ts" name="search-advance">
import { nextTick, onMounted, ref } from 'vue'
import searchBox from '@/components/searchCriteria/index.vue'
import companyList from './components/companyList.vue'
import saveConditionListModal from './components/saveConditionListModal.vue'
import saveConditionModal from './components/saveConditionModal.vue'
import { searchFormItemType } from '~/types/common/searchForm'
import { has, isEmpty } from 'lodash-es'
import { randomUUID } from '@/utils/util'
import { useRoute } from 'vue-router'
import { useEventBus } from '@vueuse/core'
import { useUserStore } from '@/store'

const { emit: openVipModal } = useEventBus('openVipModal')
const userStore = useUserStore()

const searchBoxRef = ref<InstanceType<typeof searchBox>>()
const saveConditionModalRef = ref<InstanceType<typeof saveConditionModal>>()
const companyListRef = ref<InstanceType<typeof companyList>>()
const saveConditionListDialogRef = ref<InstanceType<typeof saveConditionListModal>>()

const route = useRoute()
const searchForm = ref<{ conditions: Array<searchFormItemType>; isAnd: boolean }>({
  conditions: [
    // {
    //   uuid: randomUUID(),
    //   isShowSearchFieldBox: false,
    //   fieldIndex: 'open_status',
    //   searchIndex: 'open_status',
    //   fieldName: '经营状态',
    //   fieldType: 'ENUM_1',
    //   path: ['基础信息', '工商信息', '经营状态'],
    //   conditionType: 'ANY',
    //   conditionValue: true,
    //   dates: [],
    //   nums: [],
    //   values: ['存续', '迁入', '迁出', '在业']
    // }
  ],
  isAnd: true
})
const companyListSearchForm = ref<{ conditions: Array<searchFormItemType>; isAnd: boolean }>({
  conditions: [],
  isAnd: true
})

// 重置表单
function resetForm() {
  // searchBoxRef.value.resetForm()
  searchForm.value.conditions = [
    // {
    //   uuid: randomUUID(),
    //   isShowSearchFieldBox: false,
    //   fieldIndex: 'open_status',
    //   searchIndex: 'open_status',
    //   fieldName: '经营状态',
    //   fieldType: 'ENUM_1',
    //   path: ['基础信息', '工商信息', '经营状态'],
    //   conditionType: 'ANY',
    //   conditionValue: true,
    //   dates: [],
    //   nums: [],
    //   values: ['存续', '迁入', '迁出', '在业']
    // }
  ]
  searchForm.value.isAnd = true
  nextTick(() => {
    companyListRef.value?.resetCompanyList()
    // searchBoxRef.value.setDefaultSearchForm()
  })
}
// 搜索按钮
async function handlerSubmit() {
  try {
    const searchData = await searchBoxRef.value?.submit()
    if (!isEmpty(searchData)) {
      console.log('searchData: ', searchData)
      companyListSearchForm.value = { isAnd: searchForm.value.isAnd, ...searchData }
      console.log('companyListSearchForm.value: ', companyListSearchForm.value)
      companyListRef.value?.handlerSearch()
    }
  } catch (error) {
    console.error(error)
  }
}

// 保存搜索条件
async function saveCondition() {
  try {
    const searchData = await searchBoxRef.value?.submit()
    if (!isEmpty(searchData)) {
      saveConditionModalRef.value?.onOpen({ ...searchForm.value })
    }
  } catch (error) {
    console.error(error)
  }
}

// 处理保存条件列表点击事件
function handlerConditionListClick(searchData: { conditions: Array<searchFormItemType>; isAnd: boolean }) {
  console.log('searchData: ', searchData)
  searchForm.value.conditions = searchData.conditions
  searchForm.value.isAnd = searchData.isAnd
  handlerSubmit()
}

// 当保存条件成功需要刷新条件数量
function handlerRefreshConditionList() {
  saveConditionListDialogRef.value?.getConditionListTotal()
}

// 搜索组件加载完成回掉
async function handlerMountedDone() {
  const { query } = route
  if (has(query, 'entName')) {
    handlerSubmit()
  }
}

// 判断urlquery参数上有无entname，有就添加到搜索参数内
function handlerAddEntNameQueryParam() {
  const { query } = route
  if (has(query, 'entName')) {
    nextTick(() => {
      searchForm.value.conditions.push({
        uuid: randomUUID(),
        isShowSearchFieldBox: false,
        fieldIndex: 'search_content',
        searchIndex: 'search_content',
        fieldName: '企业名称',
        fieldType: 'STRING',
        path: ['商瞳优选', '工商信息', '企业名称'],
        conditionType: 'ANY',
        conditionValue: true,
        dates: [],
        nums: [],
        values: [query['entName'] as string]
      })
      // handlerSubmit()
    })
  }
}

onMounted(() => {
  handlerAddEntNameQueryParam()

  nextTick(() => {
    const { activity } = route.query
    if (activity === 'openSavedSearch') {
      saveConditionListDialogRef.value?.onOpen()
    }
  })
})

// onActivated(async () => {
//   const { activity } = route.query
//   await nextTick()
//   if (activity === 'openConditionListModal') {
//     saveConditionListDialogRef.value?.onOpen()
//   }
// })
</script>

<style lang="less" scoped>
.advance {
  .saveConditionBox {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .isAnd {
      border: 1px solid #6553ee;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      p {
        display: inline-block;
        line-height: 28px;
        text-align: center;
        width: 32px;
        cursor: pointer;
        color: var(--g-primary-color, #6553ee);
        &:hover {
          background-color: var(--g-primary-color, #6553ee);
          color: #fff;
        }
      }

      .activation {
        background-color: var(--g-primary-color, #6553ee);
        color: #fff;
      }
    }
    .condition {
      float: right;
    }
  }

  .submitBtn {
    text-align: right;
  }

  .companyList {
    margin-top: 16px;
  }
}
</style>
