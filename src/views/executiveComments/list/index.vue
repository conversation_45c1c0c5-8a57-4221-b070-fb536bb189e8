<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-21 11:23:00
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-10 11:40:16
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/list/index.vue
 * @Description: 
-->
<template>
  <div class="executiveCommentsOverview relative flex">
    <a-collapse
      v-if="isShowFilter"
      :activeKey="activeFilterClassify"
      ghost
      accordion
      class="navBox sticky top-80px pr-16px w100px min-w-100px"
      @change="handlerCollapseChange"
    >
      <a-collapse-panel v-for="item in executiveCommentsFilterClassifyData" :key="item.value" :showArrow="false" class="navItem">
        <template #header>
          <div class="navTitle">
            <a :class="[item.value === activeFilterClassify ? 'active' : '']">
              {{ item.label }}
            </a>
          </div>
        </template>
      </a-collapse-panel>
    </a-collapse>

    <div class="flex-1">
      <a-row :gutter="[16, 16]">
        <a-col span="16">
          <a-row :gutter="[16, 16]">
            <a-col span="24">
              <comments :industryId="props.industryId" :industryName="props.industryName" :type="activeFilterClassify" />
            </a-col>
          </a-row>
        </a-col>
        <a-col span="8">
          <activeExecutives
            v-show="activeFilterClassify === '1' || activeFilterClassify === '2'"
            class="mb-16px"
            :industryId="props.industryId"
            :industryName="props.industryName"
            :type="activeFilterClassify"
          />

          <div class="sticky t-80px">
            <executivesEnterprises
              v-show="activeFilterClassify === '2'"
              class="mb-16px"
              :industryId="props.industryId"
              :industryName="props.industryName"
              :type="activeFilterClassify"
            />
            <followExecutives v-show="activeFilterClassify === '3'" class="mb-16px" />
            <companyWrapper />
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script lang="ts">
export default defineComponent({
  name: 'executiveComments-index'
})
</script>

<script setup lang="ts">
import companyWrapper from '@/components/companyWrapper/index.vue'
import comments from './components/comments.vue'
import activeExecutives from './components/activeExecutives.vue'
import executivesEnterprises from './components/executivesEnterprises.vue'
import followExecutives from './components/followExecutives.vue'
import { computed, defineComponent, ref } from 'vue'
import { isEmpty } from 'lodash-es'
import { useUserStore } from '@/store'

const userStore = useUserStore()

const props = withDefaults(
  defineProps<{
    type?: string // 1行业概况，2全部企业，3关注企业
    industryId?: string // type1时传，行业id
    industryName?: string // type1时传，行业名称
  }>(),
  { type: '3' }
)

const isShowFilter = computed(() => props.type !== '1')
const activeFilterClassify = ref(props.type)
const executiveCommentsFilterClassifyData = [
  { label: '关注高管', value: '3' },
  { label: '更多高管', value: '2' }
]
function handlerCollapseChange(_key: string) {
  if (isEmpty(userStore.getToken) && _key === '3') {
    return false
  }

  activeFilterClassify.value = _key
}
</script>

<style lang="less" scoped>
.navBox {
  height: fit-content;
  max-height: calc(100vh - 40px);

  ::v-deep .ant-collapse-content-box {
    padding: 0;
    padding-block: 0 !important;
  }
  ::v-deep .ant-collapse-header {
    padding: 0;
    padding-inline-start: 0 !important;
  }

  .navItem {
    text-align: center;
    margin-bottom: 4px;
    .navTitle {
      a {
        color: #000;
        cursor: pointer;
        line-height: 40px;
        border-radius: 6px;
        font-weight: 500;
        display: block;
        &:hover {
          color: #fff;
          background-color: #6553ee;
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }
      }

      .active {
        color: #fff;
        background-color: #6553ee;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
      }
    }
  }
}
</style>
