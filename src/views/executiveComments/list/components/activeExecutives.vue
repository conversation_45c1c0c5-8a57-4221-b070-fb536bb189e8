<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-21 11:27:52
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-17 18:13:29
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/list/components/activeExecutives.vue
 * @Description: 活跃高管
-->
<template>
  <a-card :bordered="false" class="activity" title="活跃高管">
    <a-config-provider :theme="{ components: { Table: { fontSize: '16px' } } }">
      <a-table size="small" :dataSource="dataList" :columns="columns" :loading="loading" :pagination="false">
        <template #bodyCell="{ text, column, record }: { text: string, column: TableColumnProps, record: listActiveExecutiveResType }">
          <template v-if="column.dataIndex === 'executiveName'">
            <div class="executiveBox flex align-center" :title="record.postList.map(item => item.post).join('、')">
              <div class="w-100px h-44px lh-44px ellipsis">
                <router-link
                  class="color-#000000E0"
                  :to="{
                    name: 'executiveComments-detail',
                    path: '/executiveComments/detail',
                    query: { executiveId: record.executiveId, executiveName: record.executiveName }
                  }"
                  >{{ text }}</router-link
                >
              </div>
              <div class="flex-1 flex flex-direction-column justify-center fs-14px ellipsis">
                <p class="ellipsis">
                  {{ record.postList.map(item => item.post).join('、') }}
                </p>
                <router-link
                  class="color-#7F7F7F ellipsis"
                  :to="{
                    path: '/companyInfo/index',
                    name: 'companyInfo-index',
                    query: { companyId: record.companyUniId, companyName: record.companyName }
                  }"
                >
                  {{ record.companyName }}
                </router-link>
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'num'">
            <span :style="{ color: theme.getColorPrimary }">
              {{ text ? text : '-' }}
            </span>
          </template>
        </template>
      </a-table>
    </a-config-provider>
  </a-card>
</template>

<script setup lang="ts">
import { executiveSaidListActiveExecutive } from '@/api/api'
import { useThemeStore } from '@/store'
import { TableColumnProps } from 'ant-design-vue'
import { onMounted, ref, watch } from 'vue'
import { listActiveExecutiveResType } from '~/types/api/executiveSaid/listActiveExecutive'
import useRequest from '@/hooks/useRequest'

const props = defineProps<{ type: string; industryId?: string; industryName?: string }>()
const theme = useThemeStore()

const columns = [
  { title: '高管', dataIndex: 'executiveName', ellipsis: true },
  { title: '言论次数', dataIndex: 'num', width: 80, align: 'right' }
]

const {
  loading,
  dataList,
  getData: getList
} = useRequest(executiveSaidListActiveExecutive, {
  followCompany: props.type === '3',
  industry: props.industryName
})

watch([() => props.type, () => props.industryId], newVal => {
  console.log('newVal: ', newVal)
  getList()
})

onMounted(() => {})
</script>

<style lang="less" scoped></style>
