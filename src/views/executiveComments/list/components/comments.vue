<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-21 11:25:37
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 11:25:14
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/list/components/comments.vue
 * @Description: 高管言论
-->
<template>
  <div class="commentsList">
    <a-card :bordered="false" :bodyStyle="{ padding: '0' }">
      <template #title>
        <div class="flex items-center">
          <p :style="{ color: theme.getColorPrimary, marginRight: '8px' }">高管言论</p>

          <div class="flex items-center">
            <iconfontIcon
              icon="icon-search"
              :extra-common-props="{ class: 'fs-18px hoverPrimaryColor mr-8px color-#7F7F7F' }"
              @click="handlerSearchIconBtnClick"
            />

            <a-input
              v-show="showKeyWordInput"
              ref="keywordRef"
              v-model:value="form.keyword"
              style="width: 220px"
              class="!fs-14 fw-400 transition-all"
              :placeholder="type === '3' ? '在关注高管中搜索' : type === '2' ? '在所有高管中搜索' : '关键词搜索'"
              size="small"
              @change="handlerKeyWordChange"
              @blur="handlerKeyWordBlur"
              allowClear
            ></a-input>
          </div>
        </div>
      </template>
      <template #extra>
        <a-switch v-model:checked="form.isSelected" checked-children="精选" un-checked-children="全部" @change="refresh" />
      </template>
    </a-card>

    <div class="listContent">
      <a-config-provider>
        <a-list :loading="infiniteLoading" item-layout="horizontal" :data-source="dataList">
          <template #renderItem="{ item }: { item: executiveSaidListResType }">
            <a-card
              :bordered="false"
              class="mt-16px"
              :bodyStyle="{
                padding: 0,
                borderRadius: '8px',
                overflow: 'hidden'
              }"
            >
              <executiveSaidCard :item="item" />
            </a-card>
          </template>
          <template #loadMore>
            <div v-if="dataList.length > 0" class="loadMore py-8px">
              <div
                v-if="isEmpty(userStore.getToken)"
                class="inline-flex items-center justify-center hoverPrimaryColor endText"
                @click="showLoginModal"
              >
                <iconfontIcon icon="icon-lock-on" />
                <p class="mx4px">登录即可获取更多数据</p>
                <iconfontIcon icon="icon-chevron-down" />
              </div>
              <div v-else v-intersection-observer="handlerIntersectionObserver" class="endText">
                <p v-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
                <p v-else>没有更多了</p>
              </div>
            </div>
          </template>
        </a-list>
        <template #renderEmpty>
          <empty empty-text="没有关注的高管" />
        </template>
        <router-view />
      </a-config-provider>
    </div>
  </div>
</template>

<script setup lang="ts">
import { executiveSaidList } from '@/api/api'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import { useThemeStore, useUserStore } from '@/store'
import { InputRef } from 'ant-design-vue/es/vc-input/inputProps'
import { debounce, isEmpty } from 'lodash-es'
import { computed, nextTick, ref, watch } from 'vue'
import { vIntersectionObserver } from '@vueuse/components'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { executiveSaidListReqType, executiveSaidListResType } from '~/types/api/executiveSaid/executiveSaidList'
import showLoginModal from '@/components/loginModal'
import executiveSaidCard from '@/components/executiveSaidCard/index.vue'
import empty from '@/components/empty/index.vue'

const props = defineProps<{ type: string; industryId?: string; industryName?: string }>()
const theme = useThemeStore()
const userStore = useUserStore()

const form = ref({ keyword: undefined, isSelected: false })
const filterParams = computed<executiveSaidListReqType>(() => ({
  ...form.value,
  industry: props.industryName,
  showPostType: 1,
  relationExecutive: props.type === '3'
}))
const { dataList, loading: infiniteLoading, noMore, onLoadMore, refresh } = useInfiniteLoading(executiveSaidList, filterParams)

watch([() => props.type, () => props.industryId], newVal => {
  console.log('newVal: ', newVal)
  refresh()
})

/**
 * @description: 滚动到界面底部回调方法
 * @param {*} intersectionObserverList
 * @return {*}
 */
function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
  const { isIntersecting } = intersectionObserverList[0]
  if (isIntersecting && !infiniteLoading.value && !noMore.value) {
    onLoadMore()
  }
}

const keywordRef = ref<InputRef>()
const showKeyWordInput = ref(false)
/** 关键字输入框icon点击事件 */
function handlerSearchIconBtnClick() {
  showKeyWordInput.value = true
  nextTick(() => keywordRef.value?.focus())
}
/** 关键字变动 */
const handlerKeyWordChange = debounce(refresh, 300)
/** 关键字失焦处理 */
function handlerKeyWordBlur() {
  console.log('arguments: ', arguments)
  if (isEmpty(filterParams.value.keyword)) {
    showKeyWordInput.value = false
  }
}
</script>

<style lang="less" scoped>
.listContent {
  .titleContent {
    margin-bottom: 4px;
    .companyName {
      margin-bottom: 0px;
    }
  }

  .mainContent {
    .time {
      margin-bottom: 4px;
    }
    .content {
      margin-bottom: 4px;
      white-space: pre-wrap;
    }
  }
}
</style>
