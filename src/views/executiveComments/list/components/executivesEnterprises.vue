<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-01-22 17:17:23
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-16 16:44:34
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/list/components/executivesEnterprises.vue
 * @Description: 行业活动
-->
<template>
  <a-card :bordered="false" class="activity" title="高管活跃企业">
    <a-spin :spinning="loading">
      <a-config-provider :theme="{ components: { Table: { fontSize: '16px' } } }">
        <a-table size="small" :dataSource="dataList" :columns="columns" :loading="loading" :pagination="false">
          <template #bodyCell="{ text, column, record }: { text: string, column: TableColumnProps, record: listActiveCompanyResType }">
            <template v-if="column.dataIndex === 'companyName'">
              <span
                class="color-#000000E0 ellipsis hoverPrimaryColor"
                @click="openCompanyInfo({ companyId: record.companyUniId, companyName: record.companyName })"
              >
                {{ record.companyName }}
              </span>
            </template>
            <template v-if="column.dataIndex === 'num'">
              <span :style="{ color: theme.getColorPrimary }">
                {{ text ? text : '-' }}
              </span>
            </template>
          </template>
        </a-table>
      </a-config-provider>
    </a-spin>
  </a-card>
</template>

<script lang="ts">
export default {
  name: 'activity'
}
</script>

<script setup lang="ts">
import { executiveSaidListActiveCompany } from '@/api/api'
import { useThemeStore } from '@/store'
import { TableColumnProps } from 'ant-design-vue'
import { onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { listActiveCompanyResType } from '~/types/api/executiveSaid/listActiveCompany'

const props = defineProps<{ type: string; industryId?: string; industryName?: string }>()
const theme = useThemeStore()

const columns = [
  { title: '企业', dataIndex: 'companyName', ellipsis: true },
  { title: '言论次数', dataIndex: 'num', width: 80, align: 'right' }
]

const loading = ref(true)
const dataList = ref<listActiveCompanyResType[]>([])

async function getList() {
  try {
    loading.value = true
    const { result } = await executiveSaidListActiveCompany({ followCompany: props.type === '3', industry: props.industryName })
    dataList.value = result
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}

watch([() => props.type, () => props.industryId], newVal => {
  console.log('newVal: ', newVal)
  getList()
})

const router = useRouter()
function openCompanyInfo(item: { companyId: string; companyName: string }) {
  router.push({
    name: 'companyInfo-index',
    path: '/companyInfo/index',
    query: {
      companyId: item.companyId,
      companyName: item.companyName
    }
  })
}

onMounted(() => {})
</script>

<style lang="less" scoped></style>
