<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-01-15 15:57:07
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-17 15:02:31
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/interpret/index.vue
 * @Description: 
-->
<template>
  <div class="executiveCommentsInterpret">
    <a-row :gutter="16">
      <a-col span="16">
        <chat />
      </a-col>
      <a-col span="8">
        <followExecutives @interpret="handleInterpret" />
      </a-col>
    </a-row>
  </div>
</template>
<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import followExecutives from './components/followExecutives.vue'
import chat from './components/chat/index.vue'

const route = useRoute()
const router = useRouter()

function handleInterpret(item: { executiveId: string; executiveName: string }) {
  router.replace({ path: route.path, query: { ...item } })
}
</script>

<style lang="less" scoped>
.executiveCommentsInterpret {
}
</style>
